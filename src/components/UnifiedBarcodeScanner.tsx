"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { Html5QrcodeScanner, Html5QrcodeScanType } from "html5-qrcode";

interface UnifiedBarcodeScannerProps {
  onScan: (result: string) => void;
  onError?: (error: string) => void;
}

export default function UnifiedBarcodeScanner({
  onScan,
  onError,
}: UnifiedBarcodeScannerProps) {
  const scannerRef = useRef<Html5QrcodeScanner | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<
    "requesting" | "granted" | "denied" | null
  >(null);

  // Detect mobile device
  const isMobile = useCallback(() => {
    return (
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      ) ||
      (navigator.maxTouchPoints && navigator.maxTouchPoints > 2)
    );
  }, []);

  // Request camera permission
  const requestCameraPermission = useCallback(async (): Promise<boolean> => {
    try {
      setPermissionStatus("requesting");
      console.log("🎥 Requesting camera permission...");

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: "environment", // Prefer back camera on mobile
        },
      });

      // Stop the test stream immediately
      stream.getTracks().forEach((track) => track.stop());

      setPermissionStatus("granted");
      console.log("✅ Camera permission granted");
      return true;
    } catch (error) {
      console.error("❌ Camera permission denied:", error);
      setPermissionStatus("denied");
      setError(
        "Camera permission denied. Please allow camera access and refresh the page."
      );
      onError?.("Camera permission denied");
      return false;
    }
  }, [onError]);

  // Initialize scanner
  useEffect(() => {
    const initScanner = async () => {
      try {
        console.log("🎥 Initializing unified barcode scanner...");

        // Clean up any existing scanner first
        if (scannerRef.current) {
          try {
            await scannerRef.current.clear();
            scannerRef.current = null;
          } catch (error) {
            console.log("Previous scanner cleanup:", error);
          }
        }

        const readerElement = document.getElementById("unified-qr-reader");
        if (readerElement) {
          readerElement.innerHTML = "";
        }

        // Remove any existing custom styles
        const existingStyle = document.getElementById("unified-scanner-style");
        if (existingStyle) {
          existingStyle.remove();
        }

        // Add custom CSS to hide unwanted UI elements
        const style = document.createElement("style");
        style.id = "unified-scanner-style";
        style.textContent = `
          /* Hide green banner and status messages */
          #unified-qr-reader__status_span {
            display: none !important;
          }

          /* Hide troubleshooting tips and help text */
          #unified-qr-reader__dashboard_section_csr > div:last-child {
            display: none !important;
          }

          /* Hide file selection option */
          #unified-qr-reader__filescan_input {
            display: none !important;
          }

          /* Hide camera selection dropdown if not needed */
          #unified-qr-reader__camera_selection {
            margin-top: 10px;
          }

          /* Clean up button styling */
          #unified-qr-reader button {
            margin: 5px;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
          }

          /* Hide any green success banners */
          .qr-code-success {
            display: none !important;
          }
        `;
        document.head.appendChild(style);

        // Request camera permissions first
        const hasPermission = await requestCameraPermission();
        if (!hasPermission) {
          return;
        }

        const mobile = isMobile();
        console.log("📱 Device type:", mobile ? "Mobile" : "Desktop");

        // Create scanner with optimized settings
        const scanner = new Html5QrcodeScanner(
          "unified-qr-reader",
          {
            fps: mobile ? 8 : 12,
            qrbox: mobile
              ? { width: 280, height: 180 }
              : { width: 350, height: 220 },
            aspectRatio: 1.777778,
            supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
            showTorchButtonIfSupported: true,
            showZoomSliderIfSupported: true,
            defaultZoomValueIfSupported: 2,
            rememberLastUsedCamera: true,
            experimentalFeatures: {
              useBarCodeDetectorIfSupported: true,
            },
            videoConstraints: mobile
              ? {
                  facingMode: "environment",
                  width: { ideal: 1280, max: 1920 },
                  height: { ideal: 720, max: 1080 },
                }
              : undefined,
          },
          false
        );

        scannerRef.current = scanner;

        const onScanSuccess = (decodedText: string) => {
          console.log("🎉 Barcode scanned successfully:", decodedText);
          onScan(decodedText);

          // Visual feedback
          const readerElement = document.getElementById("unified-qr-reader");
          if (readerElement) {
            readerElement.style.border = "3px solid #10B981";
            setTimeout(() => {
              readerElement.style.border = "2px solid #7c3aed";
            }, 1500);
          }
        };

        const onScanFailure = (error: string) => {
          // Only log significant errors, ignore common scanning noise
          if (
            !error.includes("No QR code found") &&
            !error.includes("NotFoundException") &&
            !error.includes("No MultiFormat Readers")
          ) {
            console.log("🔍 Scan error:", error);
          }
        };

        scanner.render(onScanSuccess, onScanFailure);
        setIsScanning(true);
        setError(null);
        console.log("✅ Scanner started successfully");

        // Auto-start scanning by programmatically clicking the start button
        const autoStartScanner = () => {
          const startButton = document.querySelector(
            "#unified-qr-reader__camera_start_button"
          ) as HTMLButtonElement;
          if (startButton) {
            startButton.click();
            console.log("🎬 Auto-started scanner");
            return true;
          } else {
            // Fallback: try to find any start button
            const buttons = document.querySelectorAll(
              "#unified-qr-reader button"
            );
            for (const button of buttons) {
              if (
                button instanceof HTMLButtonElement &&
                (button.textContent?.toLowerCase().includes("start") ||
                  button.textContent?.toLowerCase().includes("camera"))
              ) {
                button.click();
                console.log("🎬 Auto-started scanner (fallback)");
                return true;
              }
            }
          }
          return false;
        };

        // Try to auto-start with retries
        let attempts = 0;
        const maxAttempts = 5;
        const tryAutoStart = () => {
          attempts++;
          if (autoStartScanner()) {
            return; // Success
          }

          if (attempts < maxAttempts) {
            console.log(
              `🔄 Auto-start attempt ${attempts} failed, retrying...`
            );
            setTimeout(tryAutoStart, 500);
          } else {
            console.log("⚠️ Auto-start failed after all attempts");
          }
        };

        setTimeout(tryAutoStart, 500);
      } catch (error) {
        console.error("❌ Scanner initialization failed:", error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Scanner initialization failed";
        setError(errorMessage);
        onError?.(errorMessage);
      }
    };

    initScanner();

    // Cleanup function
    return () => {
      if (scannerRef.current) {
        try {
          scannerRef.current.clear();
        } catch (error) {
          console.log("Scanner cleanup error:", error);
        }
      }
    };
  }, [onScan, onError, requestCameraPermission, isMobile]);

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Permission status */}
      {permissionStatus === "requesting" && (
        <div className="mb-4 bg-blue-900 border border-blue-700 text-blue-300 px-4 py-3 rounded">
          <p className="text-sm">📷 Requesting camera permission...</p>
        </div>
      )}

      {permissionStatus === "denied" && (
        <div className="mb-4 bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
          <p className="text-sm">
            ❌ Camera access denied. Please allow camera access and refresh the
            page.
          </p>
        </div>
      )}

      {error && (
        <div className="mb-4 bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
          <p className="text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Scanner container */}
      <div className="mb-4">
        <div
          id="unified-qr-reader"
          className="border-2 border-purple-600 rounded-lg overflow-hidden"
          style={{ minHeight: "300px" }}
        />
      </div>

      {/* Status indicator */}
      {isScanning && permissionStatus === "granted" && (
        <div className="text-center text-sm text-gray-400">
          <p>📱 Point your camera at a barcode to scan</p>
        </div>
      )}
    </div>
  );
}
