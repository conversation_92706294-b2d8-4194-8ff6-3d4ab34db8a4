"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { Html5QrcodeScanner, Html5QrcodeScanType } from "html5-qrcode";
import { But<PERSON> } from "@/components/ui/button";
import { Flashlight, FlashlightOff } from "lucide-react";

interface UnifiedBarcodeScannerProps {
  onScan: (result: string) => void;
  onError?: (error: string) => void;
  onScanResult?: (result: string) => void; // New callback for populating search
}

export default function UnifiedBarcodeScanner({
  onScan,
  onError,
  onScanResult,
}: UnifiedBarcodeScannerProps) {
  const scannerRef = useRef<Html5QrcodeScanner | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<
    "requesting" | "granted" | "denied" | null
  >(null);
  const [scannedCode, setScannedCode] = useState<string | null>(null);
  const [torchEnabled, setTorchEnabled] = useState(false);
  const [torchSupported, setTorchSupported] = useState(false);

  // Detect mobile device
  const isMobile = useCallback(() => {
    return (
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      ) ||
      (navigator.maxTouchPoints && navigator.maxTouchPoints > 2)
    );
  }, []);

  // Request camera permission
  const requestCameraPermission = useCallback(async (): Promise<boolean> => {
    try {
      setPermissionStatus("requesting");
      console.log("🎥 Requesting camera permission...");

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: "environment", // Prefer back camera on mobile
        },
      });

      // Stop the test stream immediately
      stream.getTracks().forEach((track) => track.stop());

      setPermissionStatus("granted");
      console.log("✅ Camera permission granted");
      return true;
    } catch (error) {
      console.error("❌ Camera permission denied:", error);
      setPermissionStatus("denied");
      setError(
        "Camera permission denied. Please allow camera access and refresh the page."
      );
      onError?.("Camera permission denied");
      return false;
    }
  }, [onError]);

  // Initialize scanner
  useEffect(() => {
    const initScanner = async () => {
      try {
        console.log("🎥 Initializing unified barcode scanner...");

        // Clean up any existing scanner first
        if (scannerRef.current) {
          try {
            await scannerRef.current.clear();
            scannerRef.current = null;
          } catch (error) {
            console.log("Previous scanner cleanup:", error);
          }
        }

        const readerElement = document.getElementById("unified-qr-reader");
        if (readerElement) {
          readerElement.innerHTML = "";
        }

        // Remove any existing custom styles
        const existingStyle = document.getElementById("unified-scanner-style");
        if (existingStyle) {
          existingStyle.remove();
        }

        // Add custom CSS to hide unwanted UI elements and optimize mobile
        const style = document.createElement("style");
        style.id = "unified-scanner-style";
        style.textContent = `
          /* Hide all default buttons - we'll handle auto-start */
          #unified-qr-reader button {
            display: none !important;
          }

          /* Hide green banner and status messages */
          #unified-qr-reader__status_span {
            display: none !important;
          }

          /* Hide troubleshooting tips and help text */
          #unified-qr-reader__dashboard_section_csr > div:last-child {
            display: none !important;
          }

          /* Hide file selection option */
          #unified-qr-reader__filescan_input {
            display: none !important;
          }

          /* Hide camera selection dropdown */
          #unified-qr-reader__camera_selection {
            display: none !important;
          }

          /* Hide any green success banners */
          .qr-code-success {
            display: none !important;
          }

          /* Optimize mobile video height */
          @media (max-width: 768px) {
            #unified-qr-reader video {
              max-height: 250px !important;
              object-fit: cover !important;
            }

            #unified-qr-reader {
              min-height: 250px !important;
            }
          }

          /* Clean scanner container */
          #unified-qr-reader {
            border-radius: 12px;
            overflow: hidden;
          }
        `;
        document.head.appendChild(style);

        // Request camera permissions first
        const hasPermission = await requestCameraPermission();
        if (!hasPermission) {
          return;
        }

        const mobile = isMobile();
        console.log("📱 Device type:", mobile ? "Mobile" : "Desktop");

        // Create scanner with optimized settings
        const scanner = new Html5QrcodeScanner(
          "unified-qr-reader",
          {
            fps: mobile ? 8 : 12,
            qrbox: mobile
              ? { width: 280, height: 160 }
              : { width: 350, height: 220 },
            aspectRatio: 1.777778,
            supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
            showTorchButtonIfSupported: false, // We'll handle torch with custom button
            showZoomSliderIfSupported: false,
            defaultZoomValueIfSupported: 2,
            rememberLastUsedCamera: true,
            experimentalFeatures: {
              useBarCodeDetectorIfSupported: true,
            },
            videoConstraints: mobile
              ? {
                  facingMode: "environment",
                  width: { ideal: 1280, max: 1920 },
                  height: { ideal: 600, max: 800 }, // Reduced height for mobile
                }
              : undefined,
          },
          false
        );

        scannerRef.current = scanner;

        const onScanSuccess = (decodedText: string) => {
          console.log("🎉 Barcode scanned successfully:", decodedText);
          setScannedCode(decodedText);

          // Call the new callback to populate search instead of auto-proceeding
          if (onScanResult) {
            onScanResult(decodedText);
          } else {
            // Fallback to old behavior if onScanResult not provided
            onScan(decodedText);
          }

          // Visual feedback
          const readerElement = document.getElementById("unified-qr-reader");
          if (readerElement) {
            readerElement.style.border = "3px solid #10B981";
            setTimeout(() => {
              readerElement.style.border = "2px solid #7c3aed";
            }, 1500);
          }
        };

        const onScanFailure = (error: string) => {
          // Only log significant errors, ignore common scanning noise
          if (
            !error.includes("No QR code found") &&
            !error.includes("NotFoundException") &&
            !error.includes("No MultiFormat Readers")
          ) {
            console.log("🔍 Scan error:", error);
          }
        };

        scanner.render(onScanSuccess, onScanFailure);
        setIsScanning(true);
        setError(null);
        console.log("✅ Scanner initialized and ready");

        // Since we hide all buttons with CSS, the scanner will auto-start
        // when the user grants camera permission. No manual intervention needed.
      } catch (error) {
        console.error("❌ Scanner initialization failed:", error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Scanner initialization failed";
        setError(errorMessage);
        onError?.(errorMessage);
      }
    };

    initScanner();

    // Cleanup function
    return () => {
      if (scannerRef.current) {
        try {
          scannerRef.current.clear();
        } catch (error) {
          console.log("Scanner cleanup error:", error);
        }
      }
    };
  }, [onScan, onError, requestCameraPermission, isMobile]);

  // Toggle torch function
  const toggleTorch = useCallback(async () => {
    try {
      const video = document.querySelector(
        "#unified-qr-reader video"
      ) as HTMLVideoElement;
      if (video && video.srcObject) {
        const stream = video.srcObject as MediaStream;
        const track = stream.getVideoTracks()[0];

        if (track && "torch" in track.getCapabilities()) {
          await track.applyConstraints({
            advanced: [{ torch: !torchEnabled } as any],
          });
          setTorchEnabled(!torchEnabled);
        }
      }
    } catch (error) {
      console.log("Torch toggle failed:", error);
    }
  }, [torchEnabled]);

  // Check torch support
  useEffect(() => {
    const checkTorchSupport = () => {
      const video = document.querySelector(
        "#unified-qr-reader video"
      ) as HTMLVideoElement;
      if (video && video.srcObject) {
        const stream = video.srcObject as MediaStream;
        const track = stream.getVideoTracks()[0];

        if (track && "torch" in track.getCapabilities()) {
          setTorchSupported(true);
        }
      }
    };

    // Check periodically until video is available
    const interval = setInterval(() => {
      checkTorchSupport();
      if (torchSupported) {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isScanning, torchSupported]);

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Permission status */}
      {permissionStatus === "requesting" && (
        <div className="mb-4 bg-blue-900 border border-blue-700 text-blue-300 px-4 py-3 rounded">
          <p className="text-sm">📷 Requesting camera permission...</p>
        </div>
      )}

      {permissionStatus === "denied" && (
        <div className="mb-4 bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
          <p className="text-sm">
            ❌ Camera access denied. Please allow camera access and refresh the
            page.
          </p>
        </div>
      )}

      {error && (
        <div className="mb-4 bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
          <p className="text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Scanned code display */}
      {scannedCode && (
        <div className="mb-4 bg-green-900 border border-green-700 text-green-300 px-4 py-3 rounded">
          <p className="text-sm font-bold">✅ Barcode Scanned!</p>
          <p className="text-xs font-mono bg-green-800 px-2 py-1 rounded mt-2">
            {scannedCode}
          </p>
          <p className="text-xs mt-2 text-green-400">
            Code has been populated in the search field above
          </p>
        </div>
      )}

      {/* Scanner container with torch button */}
      <div className="mb-4 relative">
        <div
          id="unified-qr-reader"
          className="border-2 border-purple-600 rounded-lg overflow-hidden"
          style={{ minHeight: "300px" }}
        />

        {/* Custom torch button */}
        {torchSupported && isScanning && (
          <div className="absolute top-4 right-4">
            <Button
              variant="secondary"
              size="sm"
              onClick={toggleTorch}
              className="bg-black/50 hover:bg-black/70 text-white border-white/20"
            >
              {torchEnabled ? (
                <FlashlightOff className="h-4 w-4" />
              ) : (
                <Flashlight className="h-4 w-4" />
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Status indicator */}
      {isScanning && permissionStatus === "granted" && !scannedCode && (
        <div className="text-center text-sm text-gray-400">
          <p>📱 Point your camera at a barcode to scan</p>
        </div>
      )}
    </div>
  );
}
